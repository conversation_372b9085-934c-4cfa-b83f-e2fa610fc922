// deno-lint-ignore-file no-explicit-any
import express from "express";
import { Shit } from "#models/shit.ts";

const app = express();
const PORT = 3000;

app.use(express.json()); // как в Node — body-parser встроен

app.get("/", (_req: any, res: any) => {
  res.send("Hello from Express in Deno!");
});

app.post("/echo", (req: any, res: any) => {
  res.json({ youSent: req.body });
});

app.listen(PORT, () => {
  console.log(`🟢 Server running at http://localhost:${PORT}`);
});
I can see the issue! The selected code shows an import statement that's using inappropriate naming. Let me check the codebase to understand the context better and help you fix this.

Read file
server.ts
Read directory
models
Read file
shit.ts
models
The issue is that you're using inappropriate and unprofessional naming in your code. The model/interface is named "Shit" which is:

Unprofessional - This kind of naming would be completely unacceptable in any professional codebase
Poor practice - Variable and type names should be descriptive and meaningful
Potentially offensive - This could be problematic in team environments
Looking at the interface structure, it appears to represent a person or user with name and age properties. Here are some better naming options:

User - if it represents a user
Person - if it represents a person
Profile - if it represents a user profile
Member - if it represents a member
Contact - if it represents contact information
Would you like me to help you rename this to something more appropriate? I can update both the model file and the import statement in server.ts to use proper naming.

