// deno-lint-ignore-file no-explicit-any
import express from "express";
import { Shit } from "#models/shit.ts";

const app = express();
const PORT = 3000;

app.use(express.json()); // как в Node — body-parser встроен

app.get("/", (_req: any, res: any) => {
  res.send("Hello from Express in Deno!");
});

app.post("/echo", (req: any, res: any) => {
  res.json({ youSent: req.body });
});

app.listen(PORT, () => {
  console.log(`🟢 Server running at http://localhost:${PORT}`);
});
